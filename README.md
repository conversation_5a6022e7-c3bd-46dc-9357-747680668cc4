# CRM to WFM Services

## Set env vars

In the cloud environment use:

```shell
# VividSeats/Nice integration
npx arc env --add --env staging VIVID_SEATS_CLIENT_ID "xyz..."
npx arc env --add --env staging VIVID_SEATS_CLIENT_SECRET "xyz..."
npx arc env --add --env staging VIVID_SEATS_ACCESS_KEY_ID "xyz..."
npx arc env --add --env staging VIVID_SEATS_SECRET_KEY "xyz..."
npx arc env --add --env staging ASSEMBLED_API_KEY "xyz..."
npx arc env --add --env staging ASSEMBLED_SANDBOX "1"

# Zendesk to Gladly integration
npx arc env --add --env staging EXPRESS_ZENDESK_API_TOKEN "xyz..."
npx arc env --add --env staging EXPRESS_ZENDESK_EMAIL "<EMAIL>"
npx arc env --add --env staging EXPRESS_ZENDESK_SUBDOMAIN "your_subdomain"
npx arc env --add --env staging EXPRESS_GLADLY_API_URL "https://your-org.gladly.com/api/v1"
npx arc env --add --env staging EXPRESS_GLADLY_EMAIL "<EMAIL>"
npx arc env --add --env staging EXPRESS_GLADLY_API_TOKEN "xyz..."
npx arc env --add --env staging EXPRESS_ZENDESK_WEBHOOK_SECRET "xyz..."
```

Locally use .env

## Deplpoy

`npx arc deploy --env staging`

## TODO

- Docs: explain using credntials and not SSO for AWS profile
- Docs: explain staging S3 bucket
- Docs: explain installing Architect? Do. Ineed to? Does NPX cover it soemhow?
- Docs: explain Local Dev vs staging vs production and using the .env vs setting env vars.
- Docs: explain not setting a variable named AWS_REGION in staging or prod because it conflicts with Archtiect., but OK to set locally to talk to your staging bucket.

- Code: try to simplify the code, especially egestion error handling and agent mapping and conversation mapping.
- Code: pull endpoint code out of schedules and queues so that you can pull that code into any customer integration.
- Code: define a really tight space per customer in which you need to do mapping.

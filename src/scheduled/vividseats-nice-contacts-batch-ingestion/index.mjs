import arc from '@architect/functions';
import { v4 } from 'uuid';
import assert from 'assert';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';

const VIVID_SEATS_AUTH_URL = 'https://cxone.niceincontact.com/auth/token';
const VIVID_SEATS_API_URL = 'https://api-c8.incontact.com/inContactAPI/services/v31.0';

const AWS_REGION = process.env.AWS_REGION;
const VIVID_SEATS_CLIENT_ID = process.env.VIVID_SEATS_CLIENT_ID;
const VIVID_SEATS_CLIENT_SECRET = process.env.VIVID_SEATS_CLIENT_SECRET;
const VIVID_SEATS_ACCESS_KEY_ID = process.env.VIVID_SEATS_ACCESS_KEY_ID;
const VIVID_SEATS_SECRET_KEY = process.env.VIVID_SEATS_SECRET_KEY;
const ARC_STORAGE_PRIVATE_JOBS = process.env.ARC_STORAGE_PRIVATE_JOBS;

assert(AWS_REGION, 'AWS_REGION is required');
assert(VIVID_SEATS_CLIENT_ID, 'VIVID_SEATS_CLIENT_ID is required');
assert(VIVID_SEATS_CLIENT_SECRET, 'VIVID_SEATS_CLIENT_SECRET is required');
assert(VIVID_SEATS_ACCESS_KEY_ID, 'VIVID_SEATS_ACCESS_KEY_ID is required');
assert(VIVID_SEATS_SECRET_KEY, 'VIVID_SEATS_SECRET_KEY is required');
assert(ARC_STORAGE_PRIVATE_JOBS, 'ARC_STORAGE_PRIVATE_JOBS is required');

export async function handler(req) {
    const now = new Date();
    console.log(`Ingestion started: ${now.toISOString()}`);
    const startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
    const endDate = now.toISOString();
    const jobId = v4();
    const Key = `jobs/${jobId}.json`;
    const Bucket = ARC_STORAGE_PRIVATE_JOBS;

    try {
        console.log(`Ingestion: getting agents`);
        const agents = await getAgents();
        console.log(`Ingestion: getting contacts`);
        const contacts = await getContacts(startDate, endDate);
        const Body = JSON.stringify({ contacts, agents });
        console.log(`Ingestion: Sending payload to S3`);
        await new S3Client({ region: AWS_REGION }).send(new PutObjectCommand({ Bucket, Key, Body }));
        console.log(`Ingestion: publishing job to queue`);
        await arc.queues.publish({
            name: 'vividseats-assembled-contacts-batch-egestion',
            payload: { jobId, Key }
        });
    } catch (error) {
        console.error('Error:', error);
    }

    return {
        statusCode: 200,
        headers: {
            'cache-control': 'no-cache, no-store, must-revalidate, max-age=0, s-maxage=0',
            'content-type': 'application/json',
        },
        body: JSON.stringify({}),
    };
}

async function getToken() {
    const getTokenResponse = await fetch(VIVID_SEATS_AUTH_URL, {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: new URLSearchParams({
            grant_type: "password",
            client_id: VIVID_SEATS_CLIENT_ID,
            client_secret: VIVID_SEATS_CLIENT_SECRET,
            username: VIVID_SEATS_ACCESS_KEY_ID,
            password: VIVID_SEATS_SECRET_KEY,
        })
    });

    const { access_token } = await getTokenResponse.json();
    return access_token;
}

async function getContacts(startDate, endDate) {
    const token = await getToken();
    const headers = {
        "Authorization": `Bearer ${token}`,
        "Content-Type": "application/json"
    };

    const allContacts = [];

    async function fetchWithRetry(url, retries = 3) {
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                const response = await fetch(url, { headers });
                if (!response.ok) {
                    const body = await response.text();
                    throw new Error(`Error fetching ccontacts from NICE ${response.status} - ${body}`);
                }
                return await response.json();
            } catch (error) {
                console.info(`Retry ${attempt}/${retries} failed`);
                if (attempt === retries) throw error;
                await new Promise(res => setTimeout(res, 2000));
            }
        }
    }

    async function fetchPage(url) {
        const data = await fetchWithRetry(url);
        allContacts.push(...(data.contacts || []));
        if (data._links?.next) await fetchPage(data._links.next);
    }

    const initialUrl = `${VIVID_SEATS_API_URL}/contacts?startDate=${startDate}&endDate=${endDate}&top=10000`;
    await fetchPage(initialUrl);
    return allContacts;
}

async function getAgents() {
    const token = await getToken();
    const headers = {
        "Authorization": `Bearer ${token}`,
        "Content-Type": "application/json"
    };

    const response = await fetch( `${VIVID_SEATS_API_URL}/agents`, { headers });
    if (!response.ok) {
        const body = await response.text();
        throw new Error(`Error fetching agents from NICE ${response.status} - ${body}`);
    }
    const { agents } = await response.json();
    return agents;
}

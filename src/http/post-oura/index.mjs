const OURA_API_KEY = process.env.OURA_API_KEY

export async function handler(req) {
    const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-API-Key',
        'Access-Control-Allow-Methods': 'OPTIONS,POST',
    }

    if (req.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers: corsHeaders,
            body: '',
        }
    }
    const apiKey = req.headers['x-api-key']


    if (apiKey !== OURA_API_KEY) {
        return {
            statusCode: 401,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Unauthorized' }),
        }
    }

    const payload = JSON.parse(req.body || '{}')

    return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
            message: 'Payload received',
            payload,
        }),
    }
}

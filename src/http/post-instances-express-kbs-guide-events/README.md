# Zendesk to Gladly Webhook Handler

This endpoint handles Zendesk Help Center article publish/unpublish events and synchronizes them with Gladly answers.

## Overview

When a Zendesk Help Center article is published or unpublished, this webhook:

1. **Article Published**: 
   - Fetches the article content from Zendesk
   - Creates a new answer in Gladly (or updates existing one)
   - Stores the mapping between Zendesk article ID and Gladly answer ID in DynamoDB

2. **Article Unpublished**:
   - Deletes the corresponding answer from <PERSON><PERSON>
   - Removes the mapping from DynamoDB

## Required Environment Variables

Set these environment variables in your deployment:

```bash
# Zendesk API credentials
ZENDESK_API_TOKEN="your_zendesk_api_token"
ZENDESK_EMAIL="<EMAIL>"
ZENDESK_SUBDOMAIN="your_zendesk_subdomain"

# Gladly API credentials  
EXPRESS_GLADLY_API_URL="https://your-org.gladly.com/api/v1"
EXPRESS_GLADLY_EMAIL="<EMAIL>"
EXPRESS_GLADLY_API_TOKEN="your_gladly_api_token"

# Optional: Webhook signature verification
ZENDESK_WEBHOOK_SECRET="your_webhook_secret"
```

## Setting Environment Variables

### For staging/production:
```bash
npx arc env --add --env staging ZENDESK_API_TOKEN "your_token"
npx arc env --add --env staging ZENDESK_EMAIL "your_email"
npx arc env --add --env staging ZENDESK_SUBDOMAIN "your_subdomain"
npx arc env --add --env staging EXPRESS_GLADLY_API_URL "your_gladly_url"
npx arc env --add --env staging EXPRESS_GLADLY_EMAIL "your_gladly_email"
npx arc env --add --env staging EXPRESS_GLADLY_API_TOKEN "your_gladly_token"
npx arc env --add --env staging ZENDESK_WEBHOOK_SECRET "your_webhook_secret"
```

### For local development:
Add to your `.env` file:
```
ZENDESK_API_TOKEN=your_token
ZENDESK_EMAIL=your_email
ZENDESK_SUBDOMAIN=your_subdomain
EXPRESS_GLADLY_API_URL=your_gladly_url
EXPRESS_GLADLY_EMAIL=your_gladly_email
EXPRESS_GLADLY_API_TOKEN=your_gladly_token
ZENDESK_WEBHOOK_SECRET=your_webhook_secret
```

## Webhook Configuration

Configure your Zendesk webhook to send events to:
```
POST https://your-domain.com/instances-express-kbs-guide-events
```

Subscribe to these event types:
- `zen:event-type:article.published`
- `zen:event-type:article.unpublished`

## DynamoDB Table

The endpoint uses a DynamoDB table `expressarticles` with the following structure:

- **Primary Key**: `zendesk_article_id` (String)
- **Attributes**:
  - `gladly_answer_id` (String) - The corresponding Gladly answer ID
  - `created_at` (String) - ISO timestamp when mapping was created
  - `updated_at` (String) - ISO timestamp when mapping was last updated

## Error Handling

The endpoint includes:
- Webhook signature verification (if secret is configured)
- Input validation for required fields
- Retry logic for API calls with exponential backoff
- Comprehensive error logging
- Graceful handling of missing articles or answers

## Testing

You can test the webhook locally using the Architect sandbox:

```bash
npm start
```

Then send a POST request to `http://localhost:3333/instances-express-kbs-guide-events` with a sample Zendesk webhook payload.

## Deployment

Deploy using Architect:

```bash
npx arc deploy --env staging
```

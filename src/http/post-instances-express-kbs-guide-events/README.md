# Zendesk to Gladly Webhook Handler

This endpoint handles Zendesk Help Center article publish/unpublish events and synchronizes them with Gladly answers.

## Overview

When a Zendesk Help Center article is published or unpublished, this webhook:

1. **Article Published**: 
   - Fetches the article content from Zendesk
   - Creates a new answer in Gladly (or updates existing one)
   - Stores the mapping between Zendesk article ID and Gladly answer ID in DynamoDB

2. **Article Unpublished**:
   - Deletes the corresponding answer from <PERSON>ly
   - Removes the mapping from DynamoDB

## Required Environment Variables

Set these environment variables in your deployment:

```bash
# Zendesk API credentials
EXPRESS_ZENDESK_API_TOKEN="your_zendesk_api_token"
EXPRESS_ZENDESK_EMAIL="<EMAIL>"
EXPRESS_ZENDESK_SUBDOMAIN="your_zendesk_subdomain"

# Gladly API credentials
EXPRESS_GLADLY_API_URL="https://your-org.gladly.com/api/v1"
EXPRESS_GLADLY_EMAIL="<EMAIL>"
EXPRESS_GLADLY_API_TOKEN="your_gladly_api_token"

# Optional: Webhook signature verification
EXPRESS_ZENDESK_WEBHOOK_SECRET="your_webhook_secret"
```

## Setting Environment Variables

### For staging/production:
```bash
npx arc env --add --env staging EXPRESS_ZENDESK_API_TOKEN "your_token"
npx arc env --add --env staging EXPRESS_ZENDESK_EMAIL "your_email"
npx arc env --add --env staging EXPRESS_ZENDESK_SUBDOMAIN "your_subdomain"
npx arc env --add --env staging EXPRESS_GLADLY_API_URL "your_gladly_url"
npx arc env --add --env staging EXPRESS_GLADLY_EMAIL "your_gladly_email"
npx arc env --add --env staging EXPRESS_GLADLY_API_TOKEN "your_gladly_token"
npx arc env --add --env staging EXPRESS_ZENDESK_WEBHOOK_SECRET "your_webhook_secret"
```

### For local development:
Add to your `.env` file:
```
EXPRESS_ZENDESK_API_TOKEN=your_token
EXPRESS_ZENDESK_EMAIL=your_email
EXPRESS_ZENDESK_SUBDOMAIN=your_subdomain
EXPRESS_GLADLY_API_URL=your_gladly_url
EXPRESS_GLADLY_EMAIL=your_gladly_email
EXPRESS_GLADLY_API_TOKEN=your_gladly_token
EXPRESS_ZENDESK_WEBHOOK_SECRET=your_webhook_secret
```

## Webhook Configuration

Configure your Zendesk webhook to send events to:
```
POST https://your-domain.com/instances-express-kbs-guide-events
```

Subscribe to these event types:
- `zen:event-type:article.published`
- `zen:event-type:article.unpublished`

## DynamoDB Table

The endpoint uses a DynamoDB table `expressarticles` with the following structure:

- **Primary Key**: `zendesk_article_id` (String)
- **Attributes**:
  - `gladly_answer_id` (String) - The corresponding Gladly answer ID
  - `created_at` (String) - ISO timestamp when mapping was created
  - `updated_at` (String) - ISO timestamp when mapping was last updated

## Error Handling

The endpoint includes:
- Webhook signature verification (if secret is configured)
- Input validation for required fields
- Retry logic for API calls with exponential backoff
- Comprehensive error logging
- Graceful handling of missing articles or answers

## Testing

You can test the webhook locally using the Architect sandbox:

```bash
npm start
```

Then send a POST request to `http://localhost:3333/instances-express-kbs-guide-events` with a sample Zendesk webhook payload.

## Troubleshooting

### Zendesk API 404 Errors

If you see "Zendesk API error: 404 Not Found" in the logs:

1. **Check your Zendesk subdomain**: Ensure `EXPRESS_ZENDESK_SUBDOMAIN` is correct
2. **Verify API credentials**: Make sure your Zendesk email and API token have Help Center access
3. **Article availability**: The article might not be immediately available via API after the webhook fires
4. **API endpoint**: The webhook will try multiple API endpoints automatically

The webhook includes fallback logic that will create Gladly answers using the webhook payload data if the Zendesk API is unavailable.

### Environment Variables

Make sure all required environment variables are set:
- `EXPRESS_ZENDESK_API_TOKEN`
- `EXPRESS_ZENDESK_EMAIL`
- `EXPRESS_ZENDESK_SUBDOMAIN`
- `EXPRESS_GLADLY_API_URL`
- `EXPRESS_GLADLY_EMAIL`
- `EXPRESS_GLADLY_API_TOKEN`

### Webhook Signature Verification

If webhook signature verification fails, check:
- `EXPRESS_ZENDESK_WEBHOOK_SECRET` matches your Zendesk webhook configuration
- The webhook is sending the `X-Zendesk-Webhook-Signature` header

## Deployment

Deploy using Architect:

```bash
npx arc deploy --env staging
```

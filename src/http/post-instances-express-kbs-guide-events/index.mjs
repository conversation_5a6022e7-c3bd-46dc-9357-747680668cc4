import { DynamoD<PERSON>lient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand, DeleteCommand } from '@aws-sdk/lib-dynamodb';
import { createHmac } from 'crypto';

// Environment variables
const ZENDESK_API_TOKEN = process.env.ZENDESK_API_TOKEN;
const ZENDESK_EMAIL = process.env.ZENDESK_EMAIL;
const ZENDESK_SUBDOMAIN = process.env.ZENDESK_SUBDOMAIN;
const EXPRESS_GLADLY_API_URL = process.env.EXPRESS_GLADLY_API_URL;
const EXPRESS_GLADLY_EMAIL = process.env.EXPRESS_GLADLY_EMAIL;
const EXPRESS_GLADLY_API_TOKEN = process.env.EXPRESS_GLADLY_API_TOKEN;
const ZENDESK_WEBHOOK_SECRET = process.env.ZENDESK_WEBHOOK_SECRET;

// Initialize DynamoDB client
const dynamoClient = new DynamoDBClient({ region: process.env.AWS_REGION });
const docClient = DynamoDBDocumentClient.from(dynamoClient);

// Utility function for retrying API calls
async function retryApiCall(apiCall, maxRetries = 3, delay = 1000) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return await apiCall();
        } catch (error) {
            console.error(`API call attempt ${attempt} failed:`, error.message);

            if (attempt === maxRetries) {
                throw error;
            }

            // Exponential backoff
            await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt - 1)));
        }
    }
}

export async function handler(req) {
    const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Zendesk-Webhook-Signature',
        'Access-Control-Allow-Methods': 'OPTIONS,POST',
    };

    // Handle preflight requests
    if (req.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers: corsHeaders,
            body: '',
        };
    }

    // Validate required environment variables
    const requiredEnvVars = [
        'ZENDESK_API_TOKEN',
        'ZENDESK_EMAIL',
        'ZENDESK_SUBDOMAIN',
        'EXPRESS_GLADLY_API_URL',
        'EXPRESS_GLADLY_EMAIL',
        'EXPRESS_GLADLY_API_TOKEN'
    ];

    for (const envVar of requiredEnvVars) {
        if (!process.env[envVar]) {
            console.error(`Missing required environment variable: ${envVar}`);
            return {
                statusCode: 500,
                headers: corsHeaders,
                body: JSON.stringify({ error: 'Server configuration error' }),
            };
        }
    }

    try {
        // Validate webhook signature if secret is configured
        if (ZENDESK_WEBHOOK_SECRET) {
            const signature = req.headers['x-zendesk-webhook-signature'];
            if (!signature || !verifyWebhookSignature(req.body, signature, ZENDESK_WEBHOOK_SECRET)) {
                console.error('Invalid webhook signature');
                return {
                    statusCode: 401,
                    headers: corsHeaders,
                    body: JSON.stringify({ error: 'Unauthorized' }),
                };
            }
        }

        // Parse the webhook payload
        let payload;
        try {
            payload = JSON.parse(req.body || '{}');
        } catch (parseError) {
            console.error('Invalid JSON payload:', parseError);
            return {
                statusCode: 400,
                headers: corsHeaders,
                body: JSON.stringify({ error: 'Invalid JSON payload' }),
            };
        }

        console.log('Received webhook payload:', JSON.stringify(payload, null, 2));

        // Extract event information
        const eventType = payload.type;
        const articleId = payload.detail?.id;

        if (!eventType) {
            console.error('No event type found in payload');
            return {
                statusCode: 400,
                headers: corsHeaders,
                body: JSON.stringify({ error: 'Missing event type' }),
            };
        }

        if (!articleId) {
            console.error('No article ID found in payload');
            return {
                statusCode: 400,
                headers: corsHeaders,
                body: JSON.stringify({ error: 'Missing article ID' }),
            };
        }

        // Process the event based on type
        if (eventType === 'zen:event-type:article.published') {
            await handleArticlePublished(articleId);
        } else if (eventType === 'zen:event-type:article.unpublished') {
            await handleArticleUnpublished(articleId);
        } else {
            console.log(`Ignoring event type: ${eventType}`);
        }

        return {
            statusCode: 200,
            headers: corsHeaders,
            body: JSON.stringify({ message: 'Webhook processed successfully' }),
        };

    } catch (error) {
        console.error('Error processing webhook:', error);
        return {
            statusCode: 500,
            headers: corsHeaders,
            body: JSON.stringify({ error: 'Internal server error' }),
        };
    }
}

async function handleArticlePublished(articleId) {
    console.log(`Processing article published event for article ${articleId}`);
    
    try {
        // Check if we already have a mapping for this article
        const existingMapping = await getArticleMapping(articleId);
        
        if (existingMapping) {
            console.log(`Article ${articleId} already has Gladly answer ${existingMapping.gladly_answer_id}, updating...`);
            // Update existing answer
            await updateGladlyAnswer(existingMapping.gladly_answer_id, articleId);
        } else {
            console.log(`Creating new Gladly answer for article ${articleId}`);
            // Create new answer
            const gladlyAnswerId = await createGladlyAnswer(articleId);
            
            // Store the mapping
            await storeArticleMapping(articleId, gladlyAnswerId);
        }
    } catch (error) {
        console.error(`Error handling article published for ${articleId}:`, error);
        throw error;
    }
}

async function handleArticleUnpublished(articleId) {
    console.log(`Processing article unpublished event for article ${articleId}`);
    
    try {
        // Get the existing mapping
        const mapping = await getArticleMapping(articleId);
        
        if (mapping) {
            console.log(`Deleting Gladly answer ${mapping.gladly_answer_id} for article ${articleId}`);
            
            // Delete the answer from Gladly
            await deleteGladlyAnswer(mapping.gladly_answer_id);
            
            // Remove the mapping
            await deleteArticleMapping(articleId);
        } else {
            console.log(`No mapping found for article ${articleId}, nothing to delete`);
        }
    } catch (error) {
        console.error(`Error handling article unpublished for ${articleId}:`, error);
        throw error;
    }
}

// DynamoDB operations
async function getArticleMapping(articleId) {
    try {
        const result = await docClient.send(new GetCommand({
            TableName: 'zendesk-gladly-mapping',
            Key: { zendesk_article_id: articleId }
        }));
        return result.Item;
    } catch (error) {
        console.error(`Error getting mapping for article ${articleId}:`, error);
        throw error;
    }
}

async function storeArticleMapping(articleId, gladlyAnswerId) {
    try {
        await docClient.send(new PutCommand({
            TableName: 'zendesk-gladly-mapping',
            Item: {
                zendesk_article_id: articleId,
                gladly_answer_id: gladlyAnswerId,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            }
        }));
        console.log(`Stored mapping: article ${articleId} -> answer ${gladlyAnswerId}`);
    } catch (error) {
        console.error(`Error storing mapping for article ${articleId}:`, error);
        throw error;
    }
}

async function deleteArticleMapping(articleId) {
    try {
        await docClient.send(new DeleteCommand({
            TableName: 'zendesk-gladly-mapping',
            Key: { zendesk_article_id: articleId }
        }));
        console.log(`Deleted mapping for article ${articleId}`);
    } catch (error) {
        console.error(`Error deleting mapping for article ${articleId}:`, error);
        throw error;
    }
}

// Zendesk API operations
async function fetchZendeskArticle(articleId) {
    const url = `https://${ZENDESK_SUBDOMAIN}.zendesk.com/api/v2/help_center/articles/${articleId}.json`;
    const auth = Buffer.from(`${ZENDESK_EMAIL}:${ZENDESK_API_TOKEN}`).toString('base64');

    return await retryApiCall(async () => {
        const response = await fetch(url, {
            headers: {
                'Authorization': `Basic ${auth}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`Zendesk API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return data.article;
    });
}

// Gladly API operations
async function createGladlyAnswer(articleId) {
    try {
        // Fetch article details from Zendesk
        const article = await fetchZendeskArticle(articleId);
        
        // Create answer in Gladly
        const answerData = {
            name: article.title,
            description: article.title, // Using title as description for now
            audienceIds: [] // Empty for now, can be configured later
        };
        
        const response = await fetch(`${EXPRESS_GLADLY_API_URL}/answers`, {
            method: 'POST',
            headers: {
                'Authorization': `Basic ${Buffer.from(`${EXPRESS_GLADLY_EMAIL}:${EXPRESS_GLADLY_API_TOKEN}`).toString('base64')}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(answerData)
        });
        
        if (!response.ok) {
            throw new Error(`Gladly API error: ${response.status} ${response.statusText}`);
        }
        
        const result = await response.json();
        const answerId = result.id;
        
        // Add content to the answer
        await addGladlyAnswerContent(answerId, article);
        
        console.log(`Created Gladly answer ${answerId} for article ${articleId}`);
        return answerId;
    } catch (error) {
        console.error(`Error creating Gladly answer for article ${articleId}:`, error);
        throw error;
    }
}

async function updateGladlyAnswer(answerId, articleId) {
    try {
        // Fetch latest article details from Zendesk
        const article = await fetchZendeskArticle(articleId);
        
        // Update answer metadata
        const answerData = {
            name: article.title,
            description: article.title
        };
        
        const response = await fetch(`${EXPRESS_GLADLY_API_URL}/answers/${answerId}`, {
            method: 'PATCH',
            headers: {
                'Authorization': `Basic ${Buffer.from(`${EXPRESS_GLADLY_EMAIL}:${EXPRESS_GLADLY_API_TOKEN}`).toString('base64')}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(answerData)
        });
        
        if (!response.ok) {
            throw new Error(`Gladly API error: ${response.status} ${response.statusText}`);
        }
        
        // Update content
        await addGladlyAnswerContent(answerId, article);
        
        console.log(`Updated Gladly answer ${answerId} for article ${articleId}`);
    } catch (error) {
        console.error(`Error updating Gladly answer ${answerId}:`, error);
        throw error;
    }
}

async function addGladlyAnswerContent(answerId, article) {
    try {
        const contentData = {
            title: article.title,
            bodyHtml: article.body
        };
        
        const response = await fetch(`${EXPRESS_GLADLY_API_URL}/answers/${answerId}/languages/en-us/type/public`, {
            method: 'PUT',
            headers: {
                'Authorization': `Basic ${Buffer.from(`${EXPRESS_GLADLY_EMAIL}:${EXPRESS_GLADLY_API_TOKEN}`).toString('base64')}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(contentData)
        });
        
        if (!response.ok) {
            throw new Error(`Gladly API error: ${response.status} ${response.statusText}`);
        }
        
        console.log(`Added content to Gladly answer ${answerId}`);
    } catch (error) {
        console.error(`Error adding content to Gladly answer ${answerId}:`, error);
        throw error;
    }
}

async function deleteGladlyAnswer(answerId) {
    try {
        const response = await fetch(`${EXPRESS_GLADLY_API_URL}/answers/${answerId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Basic ${Buffer.from(`${EXPRESS_GLADLY_EMAIL}:${EXPRESS_GLADLY_API_TOKEN}`).toString('base64')}`
            }
        });
        
        if (!response.ok) {
            throw new Error(`Gladly API error: ${response.status} ${response.statusText}`);
        }
        
        console.log(`Deleted Gladly answer ${answerId}`);
    } catch (error) {
        console.error(`Error deleting Gladly answer ${answerId}:`, error);
        throw error;
    }
}

// Webhook signature verification
function verifyWebhookSignature(body, signature, secret) {
    try {
        // Zendesk uses HMAC-SHA256 for webhook signatures
        const expectedSignature = createHmac('sha256', secret)
            .update(body, 'utf8')
            .digest('base64');

        // Compare signatures in a timing-safe manner
        return signature === expectedSignature;
    } catch (error) {
        console.error('Error verifying webhook signature:', error);
        return false;
    }
}

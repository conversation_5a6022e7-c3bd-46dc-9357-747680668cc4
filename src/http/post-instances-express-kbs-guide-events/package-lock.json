{"name": "post-instances-express-kbs-guide-events", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "post-instances-express-kbs-guide-events", "version": "1.0.0", "dependencies": {"@architect/functions": "^8.1.9"}}, "node_modules/@architect/functions": {"version": "8.1.9", "resolved": "https://registry.npmjs.org/@architect/functions/-/functions-8.1.9.tgz", "integrity": "sha512-hyNKhpwdUxkclKVys3JaX1y/GgTqAOKmVS6Kh7+VZ8+/Gr5KnDzQixH62oqp2TjxtFKazOWthjEs9xSlG7hNJg==", "license": "Apache-2.0", "dependencies": {"@aws-lite/apigatewaymanagementapi": "^0.0.10", "@aws-lite/client": "^0.22.4", "@aws-lite/dynamodb": "^0.3.9", "@aws-lite/sns": "^0.0.8", "@aws-lite/sqs": "^0.2.4", "@aws-lite/ssm": "^0.2.5", "cookie": "^1.0.2", "cookie-signature": "^1.2.2", "csrf": "^3.1.0", "node-webtokens": "^1.0.4", "run-parallel": "^1.2.0", "run-waterfall": "^1.1.7", "uid-safe": "^2.1.5"}, "engines": {"node": ">=16"}}, "node_modules/@aws-lite/apigatewaymanagementapi": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/@aws-lite/apigatewaymanagementapi/-/apigatewaymanagementapi-0.0.10.tgz", "integrity": "sha512-fIkUYTV4TF0wnNwzvbqWou/I0bxGsgJbbawBmeKoJIc+3yc2PzOIP6RqNPmWjlUHsuI7QnymPgWljWfvExlaVg==", "license": "Apache-2.0", "engines": {"node": ">=16"}}, "node_modules/@aws-lite/client": {"version": "0.22.4", "resolved": "https://registry.npmjs.org/@aws-lite/client/-/client-0.22.4.tgz", "integrity": "sha512-52ua/U3+JXecuTtrTZ1XjSbDL2S+iyfOD/1daYRnPQ83YPNzo4BJe1iIVwtZbJB6goLyeVolxSlPmnuqF6JFvw==", "license": "Apache-2.0", "workspaces": ["plugins/acm", "plugins/apigateway", "plugins/apigatewaymanagementapi", "plugins/apigatewayv2", "plugins/cloudformation", "plugins/cloudfront", "plugins/cloudwatch-logs", "plugins/dynamodb", "plugins/iam", "plugins/lambda", "plugins/organizations", "plugins/rds-data", "plugins/route53", "plugins/s3", "plugins/sns", "plugins/sqs", "plugins/ssm", "plugins/sts"], "dependencies": {"aws4": "^1.13.0"}, "engines": {"node": ">=16"}}, "node_modules/@aws-lite/dynamodb": {"version": "0.3.9", "resolved": "https://registry.npmjs.org/@aws-lite/dynamodb/-/dynamodb-0.3.9.tgz", "integrity": "sha512-jrMAWwxoAMVJ3z0/mI/GzPM5AfGmH+xzTpNIbjg3+2WdYJRqvIf8025XJdCDtS9/4x8zogdchEp3ZelXnwYyqw==", "license": "Apache-2.0", "engines": {"node": ">=16"}}, "node_modules/@aws-lite/sns": {"version": "0.0.8", "resolved": "https://registry.npmjs.org/@aws-lite/sns/-/sns-0.0.8.tgz", "integrity": "sha512-MIzHe66kLNyzPFY/DX30uN7DlVQsnBiHPYbq/7syNWuoYSG8bkWuUX2CVIuSL7Ji5jaLpQ4lf8/VQ+SiAeoIZA==", "license": "Apache-2.0", "engines": {"node": ">=16"}}, "node_modules/@aws-lite/sqs": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/@aws-lite/sqs/-/sqs-0.2.4.tgz", "integrity": "sha512-a1M3HDdkNE/xJfASlfisAaZ8XF6FpvoJbJsH/gr6pogEFWgNQyvmPVNRElnDY7JW3ee82sEOkMukYRdAbjytNQ==", "license": "Apache-2.0", "engines": {"node": ">=16"}}, "node_modules/@aws-lite/ssm": {"version": "0.2.5", "resolved": "https://registry.npmjs.org/@aws-lite/ssm/-/ssm-0.2.5.tgz", "integrity": "sha512-1B8mZ79ySqlTEfSQ87KZ0XkmTOKQFMO3lUYUGUtwNTUncJINr6nhRWEjk128oBWwEQnpJ7NfpDPjdfg1ICe3xw==", "license": "Apache-2.0", "engines": {"node": ">=16"}}, "node_modules/aws4": {"version": "1.13.2", "resolved": "https://registry.npmjs.org/aws4/-/aws4-1.13.2.tgz", "integrity": "sha512-lHe62zvbTB5eEABUVi/AwVh0ZKY9rMMDhmm+eeyuuUQbQ3+J+fONVQOZyj+DdrvD4BY33uYniyRJ4UJIaSKAfw==", "license": "MIT"}, "node_modules/cookie": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/cookie/-/cookie-1.0.2.tgz", "integrity": "sha512-9Kr/j4O16ISv8zBBhJoi4bXOYNTkFLOqSL3UDB0njXxCXNezjeyVrJyGOWtgfs/q2km1gwBcfH8q1yEGoMYunA==", "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/cookie-signature": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.2.2.tgz", "integrity": "sha512-D76uU73ulSXrD1UXF4KE2TMxVVwhsnCgfAyTg9k8P6KGZjlXKrOLe4dJQKI3Bxi5wjesZoFXJWElNWBjPZMbhg==", "license": "MIT", "engines": {"node": ">=6.6.0"}}, "node_modules/csrf": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/csrf/-/csrf-3.1.0.tgz", "integrity": "sha512-uTqEnCvWRk042asU6JtapDTcJeeailFy4ydOQS28bj1hcLnYRiqi8SsD2jS412AY1I/4qdOwWZun774iqywf9w==", "license": "MIT", "dependencies": {"rndm": "1.2.0", "tsscmp": "1.0.6", "uid-safe": "2.1.5"}, "engines": {"node": ">= 0.8"}}, "node_modules/node-webtokens": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/node-webtokens/-/node-webtokens-1.0.4.tgz", "integrity": "sha512-Sla56CeSLWvPbwud2kogqf5edQtKNXZBtXDDpmOzAgNZjwETbK/Am6PXfs54iZPLBm8K8amZ9XWaCQwGqZmKyQ==", "license": "MIT", "engines": {"node": ">=6.6.0"}}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/random-bytes": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/random-bytes/-/random-bytes-1.0.0.tgz", "integrity": "sha512-iv7LhNVO047HzYR3InF6pUcUsPQiHTM1Qal51DcGSuZFBil1aBBWG5eHPNek7bvILMaYJ/8RU1e8w1AMdHmLQQ==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/rndm": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/rndm/-/rndm-1.2.0.tgz", "integrity": "sha512-fJhQQI5tLrQvYIYFpOnFinzv9dwmR7hRnUz1XqP3OJ1jIweTNOd6aTO4jwQSgcBSFUB+/KHJxuGneime+FdzOw==", "license": "MIT"}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/run-waterfall": {"version": "1.1.7", "resolved": "https://registry.npmjs.org/run-waterfall/-/run-waterfall-1.1.7.tgz", "integrity": "sha512-iFPgh7SatHXOG1ClcpdwHI63geV3Hc/iL6crGSyBlH2PY7Rm/za+zoKz6FfY/Qlw5K7JwSol8pseO8fN6CMhhQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/tsscmp": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/tsscmp/-/tsscmp-1.0.6.tgz", "integrity": "sha512-LxhtAkPDTkVCMQjt2h6eBVY28KCjikZqZfMcC15YBeNjkgUpdCfBu5HoiOTDu86v6smE8yOjyEktJ8hlbANHQA==", "license": "MIT", "engines": {"node": ">=0.6.x"}}, "node_modules/uid-safe": {"version": "2.1.5", "resolved": "https://registry.npmjs.org/uid-safe/-/uid-safe-2.1.5.tgz", "integrity": "sha512-KPHm4VL5dDXKz01UuEd88Df+KzynaohSL9fBh096KWAxSKZQDI2uBrVqtvRM4rwrIrRRKsdLNML/lnaaVSRioA==", "license": "MIT", "dependencies": {"random-bytes": "~1.0.0"}, "engines": {"node": ">= 0.8"}}}}
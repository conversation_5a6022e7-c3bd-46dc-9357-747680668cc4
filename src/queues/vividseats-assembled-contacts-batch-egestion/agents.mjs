const b2b_incontact = [1135863, 1140180, 1137455, 1130370, 1130371, 1130372, 1130373, 1130374, 1130375, 1130376, 1130377, 1130378, 1130379, 1130591, 1131577, 1134946, 1135864, 1135893, 1137118, 1140241];
const b2b_incontact_outbound = [1130606, 1130607];
const cs_incontact = [1119262, 1119263, 1119264, 1129230, 1140079, 1104112, 1126227, 1126890, 1127219, 1139282, 1139292, 1131931, 1132237, 1124744, 1124688, 1124703, 1124087, 1129257, 1126472, 1126437, 1126350, 1125246, 1124692, 1128702, 1124694, 1129256, 1129258, 1126740, 1124698, 1124542, 1128996, 1124701, 1124685, 1125409, 1124704, 1125391, 1126328, 1124699, 1124644, 1125669, 1124697, 1125285, 1131956, 1103880, 1124693, 1124696, 1124683, 1124705, 1124695];
const ost_contact = [1119273, 1124231, 1125042, 1125245, 1139284, 1131932, 1125597, 1124457, 1124458, 1124459, 1124460, 1124461, 1124462, 1124463, 1124464, 1125447, 1124465, 1124466, 1124467, 1124468, 1124469, 1124470, 1137679, 1137678, 1135429, 1135428, 1124471, 1124472, 1124473, 1124474, 1124475, 1124476, 1125832, 1124477, 1129465, 1129464, 1125427, 1126034, 1125426, 1124478, 1139190, 1139189, 1125436, 1133265, 1133264, 1125437, 1125462, 1124479, 1124480, 1133488, 1133487, 1132345, 1132344, 1124481, 1124482, 1124483, 1124484, 1124485, 1124486, 1124487, 1124488, 1124489, 1124490, 1132343, 1132342, 1124491, 1124492, 1125380, 1125379, 1124787, 1124493, 1126457, 1126351, 1126352, 1126450, 1126353, 1126354, 1126462, 1126355, 1126443, 1126470, 1129467, 1126356, 1126446, 1133266, 1126447, 1126453, 1126357, 1133499, 1126358, 1126359, 1126438, 1126435, 1126360, 1126361, 1126362, 1126363, 1126364, 1126365, 1126366, 1126367, 1126368, 1126369, 1126370, 1126371, 1126440, 1126372, 1126373, 1126374, 1126459, 1126375, 1133500, 1126376, 1133497, 1126377, 1133496, 1126378, 1126379, 1126456, 1135114, 1126380, 1126439, 1126381, 1126601, 1133498, 1127215, 1126382, 1126452, 1126383, 1131008, 1126384, 1126385, 1126386, 1126387, 1126388, 1126389, 1126390, 1126391, 1126392, 1126393, 1126394, 1126395, 1126396, 1126397, 1128842, 1126455, 1126398, 1126441, 1126399, 1126400, 1126454, 1126401, 1126554, 1126449, 1126402, 1133889, 1131004, 1126444, 1126411, 1126403, 1126404, 1126405, 1126406, 1126407, 1126463, 1126464, 1126408, 1126465, 1126466, 1126467, 1126409, 1126468, 1126410, 1126469, 1126413, 1126458, 1126436, 1126414, 1126415, 1126416, 1126420, 1129456, 1126422, 1126423, 1126412, 1126471, 1126451, 1133885, 1129457, 1126417, 1126442, 1135881, 1126448, 1126419, 1126421, 1130524, 1126418, 1129761, 1126461, 1126445, 1126424, 1126425, 1126427, 1126428, 1126429, 1126430, 1129373, 1126431, 1126460, 1126432, 1126433, 1126434, 1135880, 1135879, 1124494, 1124496, 1124498, 1124500, 1124502, 1124504, 1124506, 1124508, 1124510, 1124512, 1124514, 1124515, 1125423, 1124516, 1124517, 1124518, 1124519, 1124520, 1124521, 1125655, 1124522, 1124523, 1124524, 1124525, 1132347, 1132346, 1133489, 1133490, 1124526, 1124527, 1124528, 1124529, 1133484, 1133483, 1135710, 1135709, 1139014, 1139013, 1124531, 1124530, 1133482, 1133481, 1124532, 1124534, 1124535, 1125592, 1131068, 1135113, 1135112, 1124537, 1124536, 1124538, 1125405, 1124539, 1124540, 1126538, 1126600, 1126599, 1124541, 1139156, 1135337, 1133494, 1133495, 1133491, 1135115, 1131005, 1126845, 1126741, 1126742, 1126838, 1126743, 1126744, 1126850, 1126745, 1126831, 1126858, 1129466, 1126746, 1126834, 1133267, 1126835, 1126747, 1126841, 1126748, 1126749, 1126826, 1126824, 1126750, 1126862, 1126752, 1126753, 1126754, 1126755, 1126756, 1126757, 1126758, 1126759, 1126760, 1126761, 1126828, 1126762, 1126763, 1126764, 1126847, 1126765, 1126766, 1133492, 1126767, 1126768, 1126769, 1126844, 1126770, 1126827, 1126771, 1126861, 1133493, 1127216, 1126772, 1126840, 1126773, 1131009, 1126774, 1126775, 1126776, 1126777, 1126778, 1126779, 1126780, 1126781, 1126782, 1126783, 1126784, 1126785, 1126786, 1126787, 1128841, 1128843, 1126788, 1126829, 1126789, 1126790, 1126842, 1126791, 1126860, 1126837, 1126792, 1133890, 1129372, 1126832, 1126801, 1126793, 1126794, 1126795, 1126796, 1126797, 1126851, 1126852, 1126798, 1126853, 1126854, 1126855, 1126799, 1126856, 1126800, 1126857, 1126803, 1126846, 1126825, 1126804, 1126805, 1126806, 1126810, 1129460, 1129461, 1126812, 1126813, 1126802, 1126859, 1126839, 1133886, 1126836, 1126809, 1126807, 1126830, 1135882, 1126808, 1126811, 1130523, 1129760, 1126849, 1126833, 1126814, 1126815, 1126816, 1126817, 1126818, 1126819, 1126820, 1126848, 1126821, 1126822, 1126823, 1133486, 1133485, 1140333, 1140332, 1127214, 1127213, 1135558, 1135559, 1124544, 1124543, 1125461, 1124545, 1124546, 1131007, 1131006, 1126865, 1126864, 1124548, 1124549, 1124550, 1124551, 1124552, 1124553, 1124554, 1124555, 1124556, 1124557, 1124558, 1124559, 1124560, 1124561, 1124562, 1124563, 1124564, 1126867, 1126866, 1124565, 1124567, 1124569, 1124570, 1124571, 1124572, 1124675, 1124573, 1126869, 1126868, 1128843, 1128839, 1138308, 1138307, 1125591, 1124574, 1124575, 1125424, 1124576, 1124577, 1124578, 1124579, 1124580, 1124581, 1125463, 1125582, 1126553, 1126552, 1125446, 1124583, 1124584, 1124585, 1124586, 1124587, 1124588, 1133888, 1133887, 1131003, 1131002, 1135427, 1135426, 1139155, 1135336, 1124589, 1124650, 1124590, 1124651, 1125429, 1124591, 1124649, 1124592, 1124593, 1124594, 1124595, 1124596, 1124597, 1124598, 1124599, 1124600, 1124601, 1124602, 1126012, 1124604, 1126013, 1124606, 1124607, 1124608, 1126014, 1124610, 1126015, 1124612, 1126016, 1126014, 1126016, 1126017, 1124618, 1124619, 1124620, 1126018, 1124621, 1124654, 1125611, 1125113, 1125114, 1124622, 1124655, 1124623, 1124657, 1124624, 1124660, 1124664, 1124625, 1129458, 1129459, 1124626, 1124667, 1124627, 1124668, 1124669, 1124628, 1124630, 1124652, 1124629, 1124653, 1126291, 1125449, 1133884, 1133883, 1124631, 1136786, 1136787, 1124666, 1125445, 1124632, 1124661, 1125425, 1124634, 1124663, 1124633, 1124665, 1130521, 1130522, 1129747, 1129750, 1124662, 1125658, 1125433, 1124635, 1124670, 1124636, 1124671, 1124637, 1124672, 1124638, 1124673, 1124677, 1124639, 1124640, 1124678, 1124679, 1124641, 1124680, 1129371, 1129370, 1124642, 1124681, 1124643, 1124682, 1124645, 1124707, 1125657, 1124646, 1124709, 1124647, 1124710, 1124648, 1124711];
const sales_incontact = [1121773, 1122169, 1127146, 1127147, 1127148, 1119259, 1119260, 1119261, 1123935, 1127143, 1129134, 1139283];
const verification_incontact = [1119270, 1127034, 1127035];
const seller_ops_incontact = [1119267, 1135684, 1136781, 1119268, 1121837, 1119275, 1121839, 1129689, 1125531, 1125532, 1125533, 1125534, 1125535, 1125536, 1125581, 1125584, 1125580, 1125537, 1125538, 1125539, 1125540, 1125541, 1125542, 1125543, 1125544, 1125545, 1125546, 1125579, 1125547, 1125548, 1125549, 1125550, 1125551, 1125552, 1125553, 1125554, 1125555, 1125556, 1125557, 1125558, 1125559, 1125560, 1125561, 1125562, 1125563, 1125564, 1125566, 1125565, 1125568, 1125567, 1125578, 1125569, 1125570, 1125571, 1125572, 1125573, 1125582, 1125574, 1125575, 1125576, 1125577, 1125450, 1124684];
const seller_ops_incontact_outbound = [1121838, 1137645, 1137644, 1119304];
const social_media_support = [1139164, 1135684];
const vdc_admin_incontact = [1139892, 1139895];
const vdc_air_ops_incontact = [];
const vdc_incontact = [1139887, 1139889, 1139893, 1139944, 1139888, 1139890, 1139944];
const vdc_incontact_outbound = [1139898, 1139946, 1139897, 1139947];
const vdc_supervisor_incontact = [];
const vsfs_incontact = [1133583, 1133586, 1133587, 1133588, 1133589, 1133590, 1133591, 1133592, 1133593, 1133594, 1133595, 1133736, 1133900, 1133995, 1134214, 1134640, 1134698, 1134702, 1134702, 1134830, 1134939, 1135010, 1135022, 1135447, 1135469, 1135486, 1135574, 1136231, 1136250, 1136301, 1136377, 1136464, 1136477, 1136718, 1136916, 1136985, 1136987, 1137029, 1137209, 1137456, 1137711, 1137873, 1137882, 1137498, 1139663, 1139661, 1138304, 1139786, 1140169, 1140041, 1140029, 1139030, 1139940, 1138946, 1138838, 1136985];

function getAgentQueues(skills) {
  if (process.env.ASSEMBLED_SANDBOX === '1') return [];

  const queues = [];
  for (const { skillId } of skills) {
    if (b2b_incontact.includes(skillId) && !queues.includes("0dfded79-036d-4433-9367-f3d8b02643a4")) queues.push("0dfded79-036d-4433-9367-f3d8b02643a4");
    if (b2b_incontact_outbound.includes(skillId) && !queues.includes("8137fad6-3247-4ace-8853-1803f33e5802")) queues.push("8137fad6-3247-4ace-8853-1803f33e5802");
    if (cs_incontact.includes(skillId) && !queues.includes("946f349c-7d6b-4404-9521-ba0668e17181")) queues.push("946f349c-7d6b-4404-9521-ba0668e17181");
    if (ost_contact.includes(skillId) && !queues.includes("f8ae6062-784b-416c-88a2-c4a4d4745b5d")) queues.push("f8ae6062-784b-416c-88a2-c4a4d4745b5d");
    if (sales_incontact.includes(skillId) && !queues.includes("4265036c-4a7d-44ea-a63d-8458dcb060b2")) queues.push("4265036c-4a7d-44ea-a63d-8458dcb060b2");
    if (verification_incontact.includes(skillId) && !queues.includes("c594c00e-32b2-4aaa-9155-b558e89e19e0")) queues.push("c594c00e-32b2-4aaa-9155-b558e89e19e0");
    if (seller_ops_incontact.includes(skillId) && !queues.includes("e26750e7-bff7-4576-a18a-ed49adb2bb18")) queues.push("e26750e7-bff7-4576-a18a-ed49adb2bb18");
    if (seller_ops_incontact_outbound.includes(skillId) && !queues.includes("a93da426-987e-4807-b87c-dde743b2fc09")) queues.push("a93da426-987e-4807-b87c-dde743b2fc09");
    if (social_media_support.includes(skillId) && !queues.includes("5a79d3b3-2f17-4ae2-8575-7ef9a538a9f7")) queues.push("5a79d3b3-2f17-4ae2-8575-7ef9a538a9f7");
    if (vdc_admin_incontact.includes(skillId) && !queues.includes("51c5fc7e-1559-44ff-841c-e0851c431dc4")) queues.push("51c5fc7e-1559-44ff-841c-e0851c431dc4");
    if (vdc_air_ops_incontact.includes(skillId) && !queues.includes("70f49676-93fb-4495-bcd4-a47c7c3bdeab")) queues.push("70f49676-93fb-4495-bcd4-a47c7c3bdeab");
    if (vdc_incontact.includes(skillId) && !queues.includes("d41177b6-b9d8-4f03-9d7d-279175b6b7df")) queues.push("d41177b6-b9d8-4f03-9d7d-279175b6b7df");
    if (vdc_incontact_outbound.includes(skillId) && !queues.includes("6c4bb206-2038-4b67-b099-2e2cecc466d7")) queues.push("6c4bb206-2038-4b67-b099-2e2cecc466d7");
    if (vdc_supervisor_incontact.includes(skillId) && !queues.includes("7c56d831-ecac-452f-9fce-33cca175629e")) queues.push("7c56d831-ecac-452f-9fce-33cca175629e");
    if (vsfs_incontact.includes(skillId) && !queues.includes("1962f2d0-122f-4e67-994c-dc972ebbe5df")) queues.push("1962f2d0-122f-4e67-994c-dc972ebbe5df");
  }

  return queues;
}

function getAgentRole(profileName) {
  if (process.env.ASSEMBLED_SANDBOX === '1') return "a4d9c816-5705-4b78-a640-16763ce595f4";

  if (["Administrator", "CapitalOne Entertainment", "Dashboard Administration APPROVED", "Default", "NICE Integration", "SkyBox", "SkyBox Manager", "VS Corporate", "Vivid Reporting API"].includes(profileName)) return "291308a2-47a2-4539-bbe7-e554402b48ce";
  if (["Alorica Agent", "TaskUs Agent"].includes(profileName)) return "64877fa5-f48e-4923-afc3-15a1a58a8bac"; // BPO Standard
  if (["Converted Agent", "VS B2B Agent", "VS Seller Operations", "VDC Agent", "VS QCI", "VS ERT", "VS Concierge Services", "VS Social Media", "VS Fraud", "VSFS"].includes(profileName)) return "7b3ddbdb-cbfd-4da2-8e15-1abbaf4d7f47"; // Standard
  if (["Alorica Manager", "TaskUs Command Center APPROVED"].includes(profileName)) return "c9ba9d6d-230a-4ac1-a218-0cc3494f94d3"; // BPO Support Staff
  if (["Alorica Command Center APPROVED", "Alorica Manager", "TaskUs Manager"].includes(profileName)) return "2f77eff9-1fe0-472e-907d-0f66b9b8df86"; // BPO Manager
  if (["Vivid Admin APPROVED", "Vivid Super Admin APPROVED"].includes(profileName)) return "6d63dbba-ad35-4a3d-9ece-6261d9787265"; // Manager
  if (["VS Command Center"].includes(profileName)) return "a952330a-9016-4739-9361-1fbba42206a2"; // Admin
  if (["Converted Manager", "VS B2B Manager", "VDC Admin", "VDC Lead", "Vivid Manager APPROVED", "VS QA", "VS Supervisor", "VS Trainers", "VSFS Manager"].includes(profileName)) return "1bd99ce3-ac5f-4a26-856d-f7d488aaca0f"; // Team Lead
  return "291308a2-47a2-4539-bbe7-e554402b48ce";
}

function getAgentChannels({ chatThreshold, emailThreshold, workItemThreshold, voiceThreshold }) {
  if (process.env.ASSEMBLED_SANDBOX === '1') return ['chat', 'email', 'phone', 'back_office'];

  const channels = [];
  if (chatThreshold > 0) channels.push("chat");
  if (emailThreshold > 0) channels.push("email");
  if (voiceThreshold > 0) channels.push("phone");
  if (workItemThreshold > 0) channels.push("back_office");
  if (channels.length === 0) channels.push("all");
  return channels;
}

function getAgentSkills(location) {
  if (process.env.ASSEMBLED_SANDBOX === '1') return [];

  if (location === "Mohali") return ["13c50df8-a39d-45c2-b7fc-d20875a6a27c"];
  if (location === "Davao") return ["0198f46e-caaf-43a9-bb35-5b7fc06346e0"];
  if (location === "Chicago") return ["4e92264d-7173-4e3b-aa33-f0fcfdbef6d2"];
  if (location === "Dallas") return ["1eadf776-7900-4e92-aa85-9468ebcc9deb"];
  if (location === "Canada") return ["41134016-c078-4887-8457-a126895666e3"];
  if (location === "Las Vegas") return ["51c035e0-8680-4599-8a69-e8d9547cd38c"];
  if (location === "Bantangas") return ["73dc402b-6e81-46f9-8dfa-545bfab61a0d"];
  if (location === "Columbia") return ["896f912b-d816-4f79-9dbc-1156b6a5d320"];
  return null;
}

function getAgentTeam(teamId) {
  if (process.env.ASSEMBLED_SANDBOX === '1') return '2b74cc2c-1878-48a0-b1bf-42e65b75213f';
  if ([803180].includes(teamId)) return "d50e125d-0a49-4093-ba38-c5749edba908"; // BPO Alorica CS
  if ([803181].includes(teamId)) return "5ab49379-3f85-449c-b0ba-8117e2ccea1c"; // BPO Alorica OST
  if ([803261].includes(teamId)) return "3a693137-ec20-4a0a-8379-6c5834d98311"; // BPO Alorica Sales
  if ([803182].includes(teamId)) return "2093ed96-6d37-4cf4-b611-d1d0019bb2f8"; // BPO Alorica Leadership
  if ([802872, 803349].includes(teamId)) return "70a7a2b2-745f-4d3e-a3cf-1382274d42d9"; // BPO TaskUs CS
  if ([802790].includes(teamId)) return "4339a5ad-5954-43a9-81e5-e5420332c50f"; // BPO TaskUs OST
  if ([803352, 803350].includes(teamId)) return "91508996-42f9-4cfe-8ab3-e7df0ee2b4da"; // BPO TaskUs Sales
  if ([802791].includes(teamId)) return "b4814351-1f96-4a21-8e72-4cdd924e0937"; // BPO TaskUs Leadership
  if ([802467].includes(teamId)) return "dfe8163f-706c-4c24-b4fe-5f93394430b9"; // B2B
  if ([800143, 801177].includes(teamId)) return "3876f5c0-2938-4dce-bc18-0a65d9dda902"; // ERT
  if ([802013].includes(teamId)) return "5af9148c-5359-4967-97f1-1b16255cde18"; // QCI
  if ([800173, 803218].includes(teamId)) return "3918da95-7053-42d5-b0aa-b2ca3dde25f0"; // Seller Operations
  if ([802854].includes(teamId)) return "1deec4ad-d976-49ef-9a44-6bc40c1ef372"; // Social Media
  if ([800162].includes(teamId)) return "8258e951-8bc7-42e9-91cd-320bf3e0c23f"; // Concierge
  if ([803371, 803372, 803374, 803373].includes(teamId)) return "bf58e27a-6b99-476c-8219-0a6cbe9bd04f"; // VDC
  if ([800147, 800367].includes(teamId)) return "95083f78-4ddc-4bc2-b05b-35adbb6884ec"; // Verification
  if ([700395].includes(teamId)) return "2d2a3401-bf31-4d57-90bd-fd47b41e5597"; // VSFS
  return null;
}

function getRestrictedSites(teamId) {
  if (process.env.ASSEMBLED_SANDBOX === '1') return [];

  const restrictedSites = [];

  if ([803180].includes(teamId)) restrictedSites.push("b8ceb812-19eb-4c91-be41-ff03582d4342"); // BPO Alorica CS Davao
  if ([803181].includes(teamId)) restrictedSites.push("4ce7603b-05c7-4c1e-a9af-37946061cc05"); // BPO Alorica OST Mohali
  if ([803261].includes(teamId)) restrictedSites.push("57ee967c-ad95-4394-bc5b-d470726729fb"); // BPO Alorica Sales Davao
  if ([803182].includes(teamId)) {
    restrictedSites.push("1b19a1fb-69eb-47e9-a82d-83fb7753c422");
    restrictedSites.push("b8ceb812-19eb-4c91-be41-ff03582d4342");
    restrictedSites.push("4ce7603b-05c7-4c1e-a9af-37946061cc05");
    restrictedSites.push("57ee967c-ad95-4394-bc5b-d470726729fb");
  }
  if ([802872].includes(teamId)) restrictedSites.push("7b2d2d65-f2a9-424e-b53b-8f9c4330bf5d"); // BPO TU CS Batangas
  if ([803349].includes(teamId)) restrictedSites.push("cb0e9a32-95a5-4bbb-85a4-4ab2636901c0"); // BPO TU CS Medellin
  if ([802790].includes(teamId)) restrictedSites.push("414417a2-626d-49ce-af18-ac9e107d322e"); // BPO TU OST Batangas
  if ([803352].includes(teamId)) restrictedSites.push("5743dd33-ddf5-4161-841e-b608b6e82abc"); // BPO TU Sales Batangas
  if ([803350].includes(teamId)) restrictedSites.push("2c6db0f1-fc84-420c-92d0-de57cfef77fd"); // BPO TU Sales Medellin
  if ([802791].includes(teamId)) {
    restrictedSites.push("33b2ffec-57fd-4606-9628-6b7009476906");
    restrictedSites.push("7b2d2d65-f2a9-424e-b53b-8f9c4330bf5d");
    restrictedSites.push("cb0e9a32-95a5-4bbb-85a4-4ab2636901c0");
    restrictedSites.push("414417a2-626d-49ce-af18-ac9e107d322e");
    // restrictedSites.push(""); // BPO TU OST Medellin - !!! NOT CONFIGURED
    restrictedSites.push("5743dd33-ddf5-4161-841e-b608b6e82abc");
    restrictedSites.push("2c6db0f1-fc84-420c-92d0-de57cfef77fd");
  }
  if ([802467].includes(teamId)) restrictedSites.push("4ad1d403-4f8a-4156-9652-02ad1e913a49"); // B2B Agents
  if ([800143].includes(teamId)) restrictedSites.push("a8fffefa-db57-4ccd-b2dd-822d002a2fe4"); // ERT Agents
  if ([801177].includes(teamId)) {
    restrictedSites.push("a8fffefa-db57-4ccd-b2dd-822d002a2fe4");
    restrictedSites.push("16a52f49-8763-41fa-8333-2df7682f8f4a");
  }
  if ([802013].includes(teamId)) {
    restrictedSites.push("d71fb7a1-9d9b-4245-adec-aef7ccf1c208");
    restrictedSites.push("5160333f-3525-4a88-8d4b-729208c205a1");
  }
  if ([800173].includes(teamId)) restrictedSites.push("cab97d51-5885-4ddf-947e-10fc77aad57f"); // Seller Operations Agents
  if ([803218].includes(teamId)) {
    restrictedSites.push("cab97d51-5885-4ddf-947e-10fc77aad57f");
    restrictedSites.push("69b91262-e01d-4f3c-aa5d-aa143f1a8faf");
  }
  if ([802854].includes(teamId)) restrictedSites.push("03c9b55a-d0ab-45c7-8653-6e7d58c51644"); // Social Media Agents
  if ([800162].includes(teamId)) restrictedSites.push("4c5021a6-a1f6-4143-96fc-18640b00bafd"); // Concierge Agents
  if ([803371].includes(teamId)) restrictedSites.push("aaf6bbfb-6e8e-4a96-ae72-e273ef1f92cf"); // VDC Air Ops Agents
  if ([803372].includes(teamId)) restrictedSites.push("305f93ac-edc5-4a85-8586-75bb497d01b4"); // VDC Contact Center Agents
  if ([803374].includes(teamId)) restrictedSites.push("6e5e7cd9-71a9-4b66-956a-6f0ed838cc33"); // VDC Ops Agents
  if ([803373].includes(teamId)) {
    restrictedSites.push("fffc0cb7-efb3-465d-bac0-2a66e09164f8");
    restrictedSites.push("aaf6bbfb-6e8e-4a96-ae72-e273ef1f92cf");
    restrictedSites.push("305f93ac-edc5-4a85-8586-75bb497d01b4");
    restrictedSites.push("6e5e7cd9-71a9-4b66-956a-6f0ed838cc33");
  }
  if ([800147].includes(teamId)) restrictedSites.push("7de5d41b-2acb-4e97-a5f2-7eaf5657e65d"); // Verification Agents
  if ([800367].includes(teamId)) restrictedSites.push("30d5f4d8-1fff-4966-8f7c-73375219dafb"); // Verification Leadership
  if ([700395].includes(teamId)) restrictedSites.push("ea480254-e86c-4ded-b458-05cf8fa9f1e5"); // VSFS Agents

  return restrictedSites;
}

function transformAgent(agent, agentSkills) {
  const {
    agentId,
    emailAddress,
    firstName,
    lastName,
    createDate,
    teamId,
    profileName,
    chatThreshold,
    emailThreshold,
    workItemThreshold,
    voiceThreshold,
    location
  } = agent;
  const agentCreationDate = createDate ? Math.floor(new Date(createDate).getTime() / 1000) : null;
  const team = getAgentTeam(teamId);
  const role = getAgentRole(profileName);

  const channels = getAgentChannels({
    chatThreshold,
    emailThreshold,
    workItemThreshold,
    voiceThreshold
  });

  const queues = getAgentQueues(agentSkills);
  const skills = getAgentSkills(location);

  const restrictedSites = getRestrictedSites(teamId);

  const data =  {
    role,
    skills,
    channels,
    teams: [team].filter(team => team),
    email: emailAddress,
    source_agent_active: true,
    source_agent_id: String(agentId),
    first_name: firstName,
    last_name: lastName,
    start_date: agentCreationDate,
    source_agent_queues: queues,
    restricted_sites: restrictedSites,
    timezone: 'America/Chicago',
    staffable: true,
    imported_id: agentId?.toString(),
    platforms: { api: agentId?.toString() },
    imported_id: agentId?.toString(),
    send_invite_email: false,
  };

  return data;
}

export { transformAgent };

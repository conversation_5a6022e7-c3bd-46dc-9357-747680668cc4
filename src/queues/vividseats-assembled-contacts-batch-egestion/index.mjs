import assert from 'assert';
import { S3Client, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { transformAgent } from './agents.mjs';
import { v4 } from 'uuid';

const VIVID_SEATS_AUTH_URL = 'https://cxone.niceincontact.com/auth/token';
const VIVID_SEATS_API_URL = 'https://api-c8.incontact.com/inContactAPI/services/v31.0';
const ASSEMBLED_API_URL = 'https://api.assembledhq.com/v0';

const AWS_REGION = process.env.AWS_REGION;
const ASSEMBLED_API_KEY = process.env.ASSEMBLED_API_KEY;
const ARC_STORAGE_PRIVATE_JOBS = process.env.ARC_STORAGE_PRIVATE_JOBS;
const VIVID_SEATS_CLIENT_ID = process.env.VIVID_SEATS_CLIENT_ID;
const VIVID_SEATS_CLIENT_SECRET = process.env.VIVID_SEATS_CLIENT_SECRET;
const VIVID_SEATS_ACCESS_KEY_ID = process.env.VIVID_SEATS_ACCESS_KEY_ID;
const VIVID_SEATS_SECRET_KEY = process.env.VIVID_SEATS_SECRET_KEY;

assert(AWS_REGION, 'AWS_REGION is required');
assert(ASSEMBLED_API_KEY, 'ASSEMBLED_API_KEY is required');
assert(VIVID_SEATS_CLIENT_ID, 'VIVID_SEATS_CLIENT_ID is required');
assert(VIVID_SEATS_CLIENT_SECRET, 'VIVID_SEATS_CLIENT_SECRET is required');
assert(VIVID_SEATS_ACCESS_KEY_ID, 'VIVID_SEATS_ACCESS_KEY_ID is required');
assert(VIVID_SEATS_SECRET_KEY, 'VIVID_SEATS_SECRET_KEY is required');
assert(ARC_STORAGE_PRIVATE_JOBS, 'ARC_STORAGE_PRIVATE_JOBS is required');

export async function handler(event) {
    for (const record of event.Records) {
        const { Key } = JSON.parse(record.body);
        const Bucket = ARC_STORAGE_PRIVATE_JOBS;
        try {
            console.log('Egestion: connecting S3');
            const { Body } = await new S3Client().send(new GetObjectCommand({ Bucket, Key }));
            console.log('Egestion: streaming payload from S3');
            const content = await handleStream(Body);
            const { contacts, agents } = JSON.parse(content);
            const conversations = transformContactsToConversations(contacts, agents);
            console.log('Egestion: sending contacts to Assembled');
            await sendToAssembled(conversations);
            console.log('Egestion: deleting payload from S3');
            await new S3Client().send(new DeleteObjectCommand({ Bucket, Key }));
        } catch (error) {
            console.error('Error:', error);
        }
    }

    return {
        statusCode: 200,
        headers: {
            'cache-control': 'no-cache, no-store, must-revalidate, max-age=0, s-maxage=0',
            'content-type': 'application/json',
        },
        body: JSON.stringify({}),
    };
}

function handleStream(stream) {
    return new Promise((resolve, reject) => {
        const chunks = [];
        stream.on("data", (chunk) => chunks.push(chunk));
        stream.on("error", reject);
        stream.on("end", () => {
            resolve(Buffer.concat(chunks).toString("utf-8"));
        });
    });
}

function transformContactsToConversations(contacts, agents) {
    return contacts.map(({
        abandoned,
        stateId,
        contactStartDate,
        preQueueSeconds,
        lastUpdateTime,
        mediaTypeId,
        agentId,
        contactId,
        isActive,
        agentStartDate,
        skillId,
        skillName,
        campaignId,
        inQueueSeconds,
        agentSeconds,
    }) => {
        const tags = [skillName, skillId?.toString()];
        const channel = getChannel(mediaTypeId);
        const agent = agents.find(({ agentId: id }) => id === agentId);
        const agentProfileTag = `agent_profile_${agent?.profileName?.replaceAll(" ", "_").toLowerCase() || 'none'}`;
        tags.push(agentProfileTag);
        if (inQueueSeconds > 0) tags.push('inbound');
        if (inQueueSeconds === 0 && agentSeconds > 0) tags.push('outbound');
        if (inQueueSeconds === 0 && agentSeconds === 0) tags.push('outbound_exclude');
        const conversation = {
            import_id: contactId?.toString(),
            assignee_import_id: agentId?.toString(),
            created_at: Math.floor(((new Date(contactStartDate)).getTime() + (preQueueSeconds * 1000)) / 1000),
            first_responded_at: agentStartDate ? Math.floor((new Date(agentStartDate)).getTime() / 1000) : null,
            solved_at: isActive ? null : Math.floor((new Date(lastUpdateTime)).getTime() / 1000),
            status: getStatus(stateId, abandoned),
            queue: getQueue(campaignId),
            channel,
            tags,
        };
        console.log(`Migrating contact: ${JSON.stringify(conversation)}`);

        return conversation;
    }
    );
}

function getQueue(campaignId) {
    if (process.env.ASSEMBLED_SANDBOX) return null;

    switch (campaignId) {
        case 901763:
            return "946f349c-7d6b-4404-9521-ba0668e17181"; // CS queue
        case 901764:
        case 902245:
        case 902388:
            return "f8ae6062-784b-416c-88a2-c4a4d4745b5d"; // OST
        case 901770:
        case 904252:
            return "4265036c-4a7d-44ea-a63d-8458dcb060b2"; // Sales
        case 901768:
        case 902798:
            return "a22f3962-b876-43eb-b22c-00d65ab9da80"; // Fraud Verification
        case 903156:
        case 903867:
        case 904057:
            return "0dfded79-036d-4433-9367-f3d8b02643a4"; // B2B
        case 903074:
        case 902350:
        case 901769:
        case 901765:
            return "e26750e7-bff7-4576-a18a-ed49adb2bb18"; // Seller Ops
        case 903586:
            return "1962f2d0-122f-4e67-994c-dc972ebbe5df"; // VSFS
        case 904383:
        case 904384:
            return "5d76a7d0-621f-4b29-a38a-6677bf8a454b"; // VDC Sales & CS
        case 904386:
            return "500bcb32-50ff-4653-aaa3-189251a66943"; // VDC Admin
        case 904387:
            return "70f49676-93fb-4495-bcd4-a47c7c3bdeab"; // VDC Air Ops
        case 904385:
            return "7c56d831-ecac-452f-9fce-33cca175629e"; // VDC Supervisor
        case 903212:
            return "8137fad6-3247-4ace-8853-1803f33e5802"; // B2B Outbound
        case 904388:
            return "6c4bb206-2038-4b67-b099-2e2cecc466d7"; // VDC Outbound
        case 904327:
            return "5a79d3b3-2f17-4ae2-8575-7ef9a538a9f7"; // Social Media Support
        default:
            return null;
    }
}

function getStatus(sourceState, abandoned) {
    if (abandoned) {
        return "abandoned";
    }

    switch (sourceState) {
        case 1:  // Abandoned
        case 24: // Refused
        case 28: // OutboundAbandon
        case 31: // OutboundCalledPartyHangUp
            return "abandoned";

        case 4:  // Active
        case 6:  // Inbound
        case 7:  // Outbound
        case 8:  // Hold
        case 9:  // Prequeue
        case 16: // EmailSent
        case 12: // Interrupted
        case 13: // Conference
        case 14: // Busy
        case 27: // PauseDetection
        case 29: // Answered
        case 34: // OutboundAnswered
            return "open";

        case 2:  // Routing
        case 5:  // Transfer
        case 10: // Inqueue
        case 15: // CallBack
        case 23: // CallbackPending
        case 25: // Preview
        case 26: // OutboundPending
        case 33: // InQeueuePreview
            return "pending";

        case 3:  // Released
        case 11: // PostQueue
        case 18: // EndContact
        case 19: // EmailDiscard
        case 20: // EmailReply
        case 21: // EmailForward
        case 22: // EmailTransfer
            return "solved";

        default:
            return "solved";
    }
}

function getChannel(mediaTypeId) {
    switch (mediaTypeId) {
        case 1: // Email
        case 2: // Fax
            return "email";
        case 3: // Chat
            return "chat";
        case 4: // Phone Call
        case 5: // Voice Mail
            return "phone";
        case 6: // Work Item
        case 7: // SMS
            return "sms";
        case 8: // Social
            return "social";
        case 9: // Digital
            return "social";
        default:
            return "email";
    }
}

async function sendToAssembled(conversations) {
    const delayed = [];
    const batches = Array(Math.ceil(conversations.length / 1000))
        .fill(0)
        .map((_, i) => conversations.slice(i * 1000, i * 1000 + 1000));

    for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        if (!batch?.length) continue;
        console.log(`Batch ${i + 1} of ${batches.length}: ${batch.length} contacts`);
        const response = await fetch('https://api.assembledhq.com/v0/conversations/bulk', {
            method: 'POST',
            headers: {
                'Authorization': 'Basic ' + Buffer.from(`${ASSEMBLED_API_KEY}:`).toString('base64'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ conversations: batch })
        });

        console.log(`Batch submitted: ${response.status}`);

        if (!response.ok) {
            const body = await response.text();
            console.log('Batch issue', body);
            const [_, agentId] = body.match(/agent with import ID: (\d+) not found/) || [];
            if (!agentId) throw new Error(`Assembled API error: ${body}`);
            try {
                const token = await getToken();
                const agent = await getAgent(agentId, token);
                const skills = await getAgentSkills(agentId, token);
                const assembledAgentFormat = transformAgent(agent, skills);
                console.log('Creating agent', agentId);
                const output = await postAgentToAssembled(assembledAgentFormat);
                console.log('Created agent', agentId);
                batches[i] = batch.filter(({ assignee_import_id }) => assignee_import_id !== agentId);
                batches.push(batch.filter(({ assignee_import_id }) => assignee_import_id === agentId));
            } catch (error) {
                console.warn('Unable to create agent', agentId);
                // Remove agent ID from convos if unable to create agent
                batches[i] = batch.map((conversation) => conversation.assignee_import_id === agentId ? Object.assign(conversation, { assignee_import_id: null }) : conversation);
            }
            i--;
        }
    }
}

async function getToken() {
    const getTokenResponse = await fetch(VIVID_SEATS_AUTH_URL, {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: new URLSearchParams({
            grant_type: "password",
            client_id: VIVID_SEATS_CLIENT_ID,
            client_secret: VIVID_SEATS_CLIENT_SECRET,
            username: VIVID_SEATS_ACCESS_KEY_ID,
            password: VIVID_SEATS_SECRET_KEY,
        })
    });

    const { access_token } = await getTokenResponse.json();
    return access_token;
}

async function getAgent(agentId, token) {
    const response = await fetch(`${VIVID_SEATS_API_URL}/agents/${agentId}`, {
        headers: { Authorization: `Bearer ${token}` }
    });

    if (!response.ok) {
        const body = await response.text();
        throw new Error(`NICE Get Agents error: ${body}, agent ID: ${agentId}`);
    }

    const { agents } = await response.json();
    return agents[0];
}

async function getAgentSkills(agentId, token) {
    const response = await fetch(`${VIVID_SEATS_API_URL}/agents/${agentId}/skills`, {
        headers: { Authorization: `Bearer ${token}` }
    });

    if (!response.ok) {
        const body = await response.text();
        throw new Error(`NICE Get Agents error: ${body}, agent ID: ${agentId}`);
    }

    const { agentSkillAssignments } = await response.json();
    return agentSkillAssignments;
}

async function postAgentToAssembled(person) {
    const response = await fetch(`${ASSEMBLED_API_URL}/people`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Authorization": "Basic " + Buffer.from(`${ASSEMBLED_API_KEY}:`).toString("base64")
        },
        body: JSON.stringify({ people: [person] })
    });

    if (!response.ok) {
        const errorBody = await response.text();
        if (errorBody.includes('try a different email') || errorBody.includes('Try using a different email')) {
            const [user, domain] = person.email.split('@');
            const email = `${user}+${v4()}@${domain}`;
            console.log('Email conflict', `${person.email} -> ${email}.`);
            return postAgentToAssembled(Object.assign(person, { email }));
        }

        if (errorBody.includes('Try using a different imported ID.')) {
            console.log('Imported ID conflict', person.imported_id, 'not importing.');
            return;
        }

        throw new Error(`Assembled API error ${response.status}: ${errorBody}`);
    }

    try {
        return await response.json();
    } catch (error) {
        throw new Error('Error creating Assembled person: unknow error, empty response.', JSON.stringify(person, null, 2));
    }
}

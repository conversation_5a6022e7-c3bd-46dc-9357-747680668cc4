# Express Guide Answers Sync

## Overview

The goal is to use Zendesk Help center articles as the source of truth, and replicate them in Gladly as answers. We will achieve this by doing an initial migration followed by listening to publish and unpublish events from Zendesk and replicating them in Gladly.

## Implementation of the webhook listener

We'll need to listen for webhook events for publish and unpublish from Zendesk. And when we receive an event we'll need to fetch the article from Zendesk to get the content fpr the article name, article description and article content in Gladly. We'll maintain a table in Dynamo for articleId to Gladly answerId mapping. If an article is published we'll create a new answer in Gladly and create the mapping if it doesn't exist. If an article is unpublished we'll delete the answer from Gladly and remove the mapping.

### Sample webhook events

#### Article published

```json
{
  "account_id": ********,
  "detail": {
    "brand_id": "*********",
    "id": "*************"
  },
  "event": {
    "author_id": "*********",
    "category_id": "*************",
    "locale": "en-us",
    "section_id": "*************",
    "title": "Dolor est quo ratione."
  },
  "id": "01GX4P86AC7T0AFF86QGAHTEFR",
  "subject": "zen:article:*************",
  "time": "2023-04-03T23:11:49.571545199Z",
  "type": "zen:event-type:article.published",
  "zendesk_event_version": "2022-11-06"
}
```

#### Article unpublished

```json
{
  "account_id": ********,
  "detail": {
    "brand_id": "*************",
    "id": "*************"
  },
  "event": {},
  "id": "01GX4PA0K4TC01K8AVSC3FW2ES",
  "subject": "zen:article:*************",
  "time": "2023-04-03T23:12:49.245377265Z",
  "type": "zen:event-type:article.unpublished",
  "zendesk_event_version": "2022-11-06"
}
```

### Gladly API

Gladly Answer management docs https://developer.gladly.com/rest/#tag/Answer-Management:

```shell
export EXPRESS_GLADLY_API_URL="https://{your‑org}.gladly.com/api/v1"
export EXPRESS_GLADLY_EMAIL="api.user@your‑org.com"
export EXPRESS_GLADLY_API_TOKEN="YOUR_API_TOKEN"
```

API calls:

```shell
############################################################
# 1. POST /answers — create a new Answer                   #
############################################################
curl -X POST "$EXPRESS_EXPRESS_GLADLY_API_URL/answers" \
  -u "$EXPRESS_GLADLY_EMAIL:$EXPRESS_GLADLY_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
        "name": "How to reset your password",
        "description": "Step‑by‑step instructions",
        "audienceIds": ["audience-1", "audience-2"]
      }'

############################################################
# 2. GET /answers/:id — fetch the Answer                   #
############################################################
ANSWER_ID="1grfSzATQLa334VDLCWc4A"

curl -X GET "$EXPRESS_EXPRESS_GLADLY_API_URL/answers/$ANSWER_ID" \
  -u "$EXPRESS_GLADLY_EMAIL:$EXPRESS_GLADLY_API_TOKEN"

############################################################
# 3. PATCH /answers/:id — update the Answer metadata       #
############################################################
curl -X PATCH "$EXPRESS_EXPRESS_GLADLY_API_URL/answers/$ANSWER_ID" \
  -u "$EXPRESS_GLADLY_EMAIL:$EXPRESS_GLADLY_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
        "name": "Password reset steps",
        "description": "Updated description",
        "audienceIds": ["audience-3"]
      }'

############################################################
# 4. DELETE /answers/:id — remove the Answer               #
############################################################
curl -X DELETE "$EXPRESS_EXPRESS_GLADLY_API_URL/answers/$ANSWER_ID" \
  -u "$EXPRESS_GLADLY_EMAIL:$EXPRESS_GLADLY_API_TOKEN"

############################################################
# 5. PUT /answers/:id/languages/:lang/type/:type           #
#    — add or replace PUBLIC content for the Answer        #
############################################################
LANG="en-us"
TYPE="public"

curl -X PUT "$EXPRESS_EXPRESS_GLADLY_API_URL/answers/$ANSWER_ID/languages/$LANG/type/$TYPE" \
  -u "$EXPRESS_GLADLY_EMAIL:$EXPRESS_GLADLY_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
        "title": "Reset your password",
        "bodyHtml": "<div><a href=\"/reset-password\">Click here</a> to reset your password</div>"
      }'

############################################################
# 6. GET the PUBLIC content we just added                  #
############################################################
curl -X GET "$EXPRESS_EXPRESS_GLADLY_API_URL/answers/$ANSWER_ID/languages/$LANG/type/$TYPE" \
  -u "$EXPRESS_GLADLY_EMAIL:$EXPRESS_GLADLY_API_TOKEN"

############################################################
# 7. DELETE the PUBLIC content                             #
############################################################
curl -X DELETE "$EXPRESS_EXPRESS_GLADLY_API_URL/answers/$ANSWER_ID/languages/$LANG/type/$TYPE" \
  -u "$EXPRESS_GLADLY_EMAIL:$EXPRESS_GLADLY_API_TOKEN"
```
